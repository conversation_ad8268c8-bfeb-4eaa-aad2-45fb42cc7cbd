-- 性能测试脚本：测试 CheckZhiShengDanKeyUse 方法的优化效果
-- 使用方法：在游戏中执行此脚本来比较优化前后的性能

local function test_performance()
    print("=== 直升丹快速使用性能测试 ===")
    
    -- 模拟多次等级变化调用
    local test_count = 1000
    local start_time = socket.gettime()
    
    -- 测试优化后的方法
    for i = 1, test_count do
        if FunctionGuide.Instance then
            FunctionGuide.Instance:CheckZhiShengDanKeyUse()
        end
    end
    
    local end_time = socket.gettime()
    local optimized_time = end_time - start_time
    
    print(string.format("优化后方法执行 %d 次耗时: %.4f 秒", test_count, optimized_time))
    print(string.format("平均每次调用耗时: %.6f 秒", optimized_time / test_count))
    
    -- 测试等级变化时的缓存效果
    print("\n=== 测试等级缓存效果 ===")
    
    -- 模拟相同等级多次调用（应该被缓存跳过）
    start_time = socket.gettime()
    for i = 1, test_count do
        if FunctionGuide.Instance then
            FunctionGuide.Instance:CheckZhiShengDanKeyUse()
        end
    end
    end_time = socket.gettime()
    local cached_time = end_time - start_time
    
    print(string.format("相同等级缓存调用 %d 次耗时: %.4f 秒", test_count, cached_time))
    print(string.format("平均每次调用耗时: %.6f 秒", cached_time / test_count))
    
    -- 计算缓存效果
    if optimized_time > 0 then
        local cache_efficiency = (optimized_time - cached_time) / optimized_time * 100
        print(string.format("缓存效率提升: %.2f%%", cache_efficiency))
    end
    
    print("\n=== 测试完成 ===")
end

-- 执行测试
test_performance()
